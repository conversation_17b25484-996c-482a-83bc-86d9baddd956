#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI内容检测接口性能测试结果分析脚本
用于分析JMeter测试结果并生成详细报告
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime
import argparse

def analyze_jtl_file(jtl_file):
    """分析JTL结果文件"""
    try:
        # 读取JTL文件
        df = pd.read_csv(jtl_file)
        
        # 基本统计信息
        total_requests = len(df)
        successful_requests = len(df[df['success'] == True])
        failed_requests = total_requests - successful_requests
        success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0
        
        # 响应时间统计
        response_times = df['elapsed']
        avg_response_time = response_times.mean()
        min_response_time = response_times.min()
        max_response_time = response_times.max()
        p90_response_time = response_times.quantile(0.9)
        p95_response_time = response_times.quantile(0.95)
        p99_response_time = response_times.quantile(0.99)
        
        # 吞吐量计算
        test_duration = (df['timeStamp'].max() - df['timeStamp'].min()) / 1000  # 转换为秒
        tps = total_requests / test_duration if test_duration > 0 else 0
        
        # 错误分析
        error_analysis = {}
        if failed_requests > 0:
            error_df = df[df['success'] == False]
            error_analysis = error_df['responseMessage'].value_counts().to_dict()
        
        return {
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'failed_requests': failed_requests,
            'success_rate': success_rate,
            'avg_response_time': avg_response_time,
            'min_response_time': min_response_time,
            'max_response_time': max_response_time,
            'p90_response_time': p90_response_time,
            'p95_response_time': p95_response_time,
            'p99_response_time': p99_response_time,
            'test_duration': test_duration,
            'tps': tps,
            'error_analysis': error_analysis
        }
    except Exception as e:
        print(f"分析文件时出错: {e}")
        return None

def generate_report(analysis_result, output_file=None):
    """生成测试报告"""
    if not analysis_result:
        return
    
    report = f"""
========================================
AI内容检测接口性能测试报告
========================================
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

基本统计信息:
- 总请求数: {analysis_result['total_requests']:,}
- 成功请求数: {analysis_result['successful_requests']:,}
- 失败请求数: {analysis_result['failed_requests']:,}
- 成功率: {analysis_result['success_rate']:.2f}%
- 测试持续时间: {analysis_result['test_duration']:.2f} 秒

响应时间统计 (毫秒):
- 平均响应时间: {analysis_result['avg_response_time']:.2f}
- 最小响应时间: {analysis_result['min_response_time']:.2f}
- 最大响应时间: {analysis_result['max_response_time']:.2f}
- 90%响应时间: {analysis_result['p90_response_time']:.2f}
- 95%响应时间: {analysis_result['p95_response_time']:.2f}
- 99%响应时间: {analysis_result['p99_response_time']:.2f}

吞吐量:
- 每秒事务数(TPS): {analysis_result['tps']:.2f}

性能评估:
"""
    
    # 性能评估
    if analysis_result['success_rate'] >= 99:
        report += "✅ 成功率: 优秀 (≥99%)\n"
    elif analysis_result['success_rate'] >= 95:
        report += "⚠️  成功率: 良好 (95-99%)\n"
    else:
        report += "❌ 成功率: 需要改进 (<95%)\n"
    
    if analysis_result['avg_response_time'] <= 1000:
        report += "✅ 平均响应时间: 优秀 (≤1秒)\n"
    elif analysis_result['avg_response_time'] <= 3000:
        report += "⚠️  平均响应时间: 良好 (1-3秒)\n"
    else:
        report += "❌ 平均响应时间: 需要改进 (>3秒)\n"
    
    if analysis_result['p95_response_time'] <= 5000:
        report += "✅ 95%响应时间: 优秀 (≤5秒)\n"
    elif analysis_result['p95_response_time'] <= 10000:
        report += "⚠️  95%响应时间: 良好 (5-10秒)\n"
    else:
        report += "❌ 95%响应时间: 需要改进 (>10秒)\n"
    
    # 错误分析
    if analysis_result['error_analysis']:
        report += "\n错误分析:\n"
        for error, count in analysis_result['error_analysis'].items():
            report += f"- {error}: {count} 次\n"
    
    # 建议
    report += "\n优化建议:\n"
    if analysis_result['success_rate'] < 99:
        report += "- 检查服务器稳定性和错误处理机制\n"
    if analysis_result['avg_response_time'] > 3000:
        report += "- 优化接口处理逻辑，考虑缓存机制\n"
    if analysis_result['p95_response_time'] > 10000:
        report += "- 检查系统瓶颈，可能需要扩容\n"
    
    report += "\n========================================\n"
    
    # 输出报告
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"报告已保存到: {output_file}")
    else:
        print(report)

def main():
    parser = argparse.ArgumentParser(description='分析JMeter性能测试结果')
    parser.add_argument('jtl_file', help='JTL结果文件路径')
    parser.add_argument('-o', '--output', help='输出报告文件路径')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.jtl_file):
        print(f"错误: 文件 {args.jtl_file} 不存在")
        sys.exit(1)
    
    print("正在分析测试结果...")
    analysis_result = analyze_jtl_file(args.jtl_file)
    
    if analysis_result:
        generate_report(analysis_result, args.output)
    else:
        print("分析失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
