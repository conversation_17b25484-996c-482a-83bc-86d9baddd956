#!/bin/bash

# AI内容检测接口快速测试脚本
# 用于快速验证接口可用性和基本性能

echo "=========================================="
echo "AI内容检测接口快速测试"
echo "=========================================="

# 检查JMeter是否安装
if ! command -v jmeter &> /dev/null; then
    echo "错误：JMeter未安装或未添加到PATH环境变量"
    exit 1
fi

# 创建临时目录
mkdir -p temp_results

# 获取当前时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "开始快速测试..."
echo "测试配置：10个用户，持续2分钟"
echo ""

# 执行快速测试
jmeter -n -t AI_Content_Check_Performance_Test.jmx \
       -l temp_results/quick_test_${TIMESTAMP}.jtl \
       -j temp_results/quick_test_${TIMESTAMP}.log \
       -Jthreads=10 \
       -Jrampup=30 \
       -Jduration=120

# 检查结果
if [ $? -eq 0 ]; then
    echo ""
    echo "快速测试完成！"
    
    # 显示基本统计
    RESULT_FILE="temp_results/quick_test_${TIMESTAMP}.jtl"
    if [ -f "$RESULT_FILE" ]; then
        echo "基本统计："
        echo "总请求数：$(grep -c "^[0-9]" "$RESULT_FILE")"
        echo "成功请求数：$(grep ",true," "$RESULT_FILE" | wc -l)"
        echo "失败请求数：$(grep ",false," "$RESULT_FILE" | wc -l)"
        
        # 计算成功率
        TOTAL=$(grep -c "^[0-9]" "$RESULT_FILE")
        SUCCESS=$(grep ",true," "$RESULT_FILE" | wc -l)
        if [ $TOTAL -gt 0 ]; then
            SUCCESS_RATE=$(echo "scale=2; $SUCCESS * 100 / $TOTAL" | bc -l 2>/dev/null || echo "计算失败")
            echo "成功率：${SUCCESS_RATE}%"
        fi
    fi
    
    echo ""
    echo "如需详细测试，请运行：./run_performance_test.sh"
else
    echo "快速测试失败，请检查日志文件"
    exit 1
fi
