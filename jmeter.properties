# JMeter性能优化配置文件
# 用于AI内容检测接口性能测试

# ========================================
# 基本配置
# ========================================

# 设置语言为中文
language=zh_CN

# 日志级别
log_level.jmeter=INFO
log_level.jmeter.engine=INFO
log_level.jmeter.threads=INFO

# ========================================
# HTTP配置优化
# ========================================

# HTTP连接超时设置
httpclient.timeout=30000
httpclient.socket.http.cps=0
httpclient.socket.https.cps=0

# HTTP连接池配置
httpclient.reset_state_on_thread_group_iteration=true
httpclient.retry_count=1

# Keep-Alive设置
httpclient.stale_check=true

# ========================================
# 性能优化配置
# ========================================

# 禁用不必要的监听器
jmeter.save.saveservice.output_format=csv
jmeter.save.saveservice.response_data=false
jmeter.save.saveservice.samplerData=false
jmeter.save.saveservice.requestHeaders=false
jmeter.save.saveservice.responseHeaders=false
jmeter.save.saveservice.encoding=false
jmeter.save.saveservice.url=true
jmeter.save.saveservice.filename=false
jmeter.save.saveservice.hostname=false
jmeter.save.saveservice.thread_counts=true
jmeter.save.saveservice.sample_count=false
jmeter.save.saveservice.idle_time=false

# 结果保存配置
jmeter.save.saveservice.timestamp_format=yyyy/MM/dd HH:mm:ss
jmeter.save.saveservice.print_field_names=true
jmeter.save.saveservice.time=true
jmeter.save.saveservice.latency=true
jmeter.save.saveservice.connect_time=true
jmeter.save.saveservice.bytes=true
jmeter.save.saveservice.sent_bytes=true
jmeter.save.saveservice.label=true
jmeter.save.saveservice.code=true
jmeter.save.saveservice.message=true
jmeter.save.saveservice.success=true
jmeter.save.saveservice.thread_name=true
jmeter.save.saveservice.data_type=true
jmeter.save.saveservice.assertion_results_failure_message=true

# ========================================
# 内存和性能优化
# ========================================

# 禁用GUI组件
jmeter.gui.action.save.graphics=false
jmeter.gui.action.save_graphics=false

# 减少内存使用
jmeter.save.saveservice.subresults=false
jmeter.save.saveservice.assertions=false

# 线程组优化
jmeter.engine.force.system.exit=true

# ========================================
# SSL/TLS配置
# ========================================

# SSL协议版本
https.default.protocol=TLS
https.socket.protocols=TLSv1.2,TLSv1.3

# 忽略SSL证书错误（仅测试环境使用）
httpclient.validate_after_inactivity=1000

# ========================================
# 报告生成配置
# ========================================

# HTML报告配置
jmeter.reportgenerator.overall_granularity=60000
jmeter.reportgenerator.apdex_satisfied_threshold=1500
jmeter.reportgenerator.apdex_tolerated_threshold=3000

# 图表配置
jmeter.reportgenerator.graph.responseTimeDistribution.property.set_granularity=100
jmeter.reportgenerator.graph.responseTimePercentiles.property.set_granularity=1000
jmeter.reportgenerator.graph.responseTimesOverTime.property.set_granularity=60000

# ========================================
# 自定义属性
# ========================================

# 测试参数（可通过命令行覆盖）
threads=100
rampup=300
duration=1800
loops=-1

# 接口配置
base_url=https://tcms.zjsnews.cn
api_path=/ai/check/

# 断言配置
response_timeout=30000
expected_response_code=200

# ========================================
# 监控配置
# ========================================

# JVM监控
jmeter.engine.nongui.port=4445
jmeter.engine.nongui.maxport=4455

# 远程测试配置（如需要）
remote_hosts=
server.rmi.ssl.disable=true

# ========================================
# 日志配置
# ========================================

# 日志文件配置
log_file=logs/jmeter.log
jmeter.loggerpanel.display=false

# 错误日志
jmeter.save.saveservice.assertion_results=failuresOnly

# ========================================
# 编码配置
# ========================================

# 默认编码
sampleresult.default.encoding=UTF-8
file.encoding=UTF-8

# ========================================
# 其他优化配置
# ========================================

# 禁用不必要的功能
jmeter.save.saveservice.autoflush=false
jmeter.save.saveservice.xml=false

# 提高性能
jmeter.engine.remote.system.exit=false
jmeter.exit.check.pause=2000

# 网络配置
httpclient.socket.http.cps=0
httpclient.socket.https.cps=0
