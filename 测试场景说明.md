# 内容审核接口多场景压力测试方案

## 🎯 测试目标
对内容审核接口进行全面的性能测试，覆盖多种真实业务场景，验证系统在不同负载下的稳定性和性能表现。

## 📊 测试场景设计

### 1. **测试数据覆盖场景**
| 场景类型 | 内容描述 | 测试目的 |
|---------|---------|---------|
| 正常新闻 | 节日文化、日常生活、科技发展等 | 验证正常业务流程 |
| 边界测试 | 空内容、短文本、特殊字符 | 测试系统边界处理能力 |
| 压力测试 | 超长文本、重复内容 | 验证大数据量处理性能 |
| 格式测试 | JSON、HTML、URL链接等 | 测试不同格式内容处理 |
| 语言测试 | 英文、混合语言、表情符号 | 验证多语言支持能力 |
| 安全测试 | HTML标签、脚本内容 | 测试安全过滤机制 |

### 2. **负载测试阶段**

#### **阶段一：预热阶段（0-5分钟）**
- **用户数**: 50个用户
- **渐增时间**: 1分钟
- **持续时间**: 5分钟
- **目的**: 系统预热，JVM优化，连接池建立

#### **阶段二：主压测阶段（5-35分钟）**
- **用户数**: 500个用户
- **渐增时间**: 5分钟
- **持续时间**: 30分钟
- **目的**: 主要性能测试，模拟高峰期负载

## 🔧 技术实现

### **CSV数据文件结构**
```csv
newsContent,contentType,scenario
内容文本,内容类型,测试场景
```

### **关键配置参数**
- **总测试时间**: 35分钟
- **最大并发用户**: 500
- **目标吞吐量**: 100 TPS
- **响应时间阈值**: 5秒
- **数据循环**: 启用，确保数据不断循环使用

## 📈 监控指标

### **性能指标**
1. **响应时间**: 平均、最小、最大、90%、95%、99%分位数
2. **吞吐量**: TPS（每秒事务数）
3. **错误率**: 各种错误类型的比例
4. **并发用户数**: 实时活跃用户数量

### **业务指标**
1. **场景覆盖率**: 各种测试场景的执行比例
2. **内容类型分布**: 不同内容类型的处理性能
3. **异常处理**: 边界情况和异常输入的处理能力

## 🎛️ 断言验证

### **基础断言**
- HTTP状态码 = 200
- 响应时间 < 5秒
- 响应内容包含"code"字段

### **高级验证**
- 响应JSON格式正确性
- 业务逻辑返回值验证
- 不同内容类型的处理结果验证

## 📋 测试执行步骤

### **准备阶段**
1. 确保`test_data.csv`文件在JMeter工作目录
2. 检查目标服务器可访问性
3. 配置监控工具（如需要）

### **执行阶段**
1. 启动JMeter GUI模式进行调试
2. 验证单个请求正常执行
3. 切换到命令行模式执行完整测试
4. 实时监控系统资源使用情况

### **结果分析**
1. 查看聚合报告了解整体性能
2. 分析不同场景的性能差异
3. 识别性能瓶颈和优化点
4. 生成测试报告和建议

## 🚨 注意事项

### **测试环境**
- 确保测试环境与生产环境配置相似
- 避免在生产环境直接执行高强度压测
- 提前通知相关团队测试计划

### **资源监控**
- 监控服务器CPU、内存、网络使用率
- 观察数据库连接池状态
- 检查应用日志中的异常信息

### **测试数据**
- 测试数据应覆盖真实业务场景
- 避免使用敏感或违规内容
- 定期更新测试数据集

## 📊 预期结果

### **性能基准**
- **平均响应时间**: < 1秒
- **95%响应时间**: < 3秒
- **错误率**: < 1%
- **目标TPS**: 100

### **场景表现**
- 正常新闻内容处理最快
- 长文本内容响应时间较长但在可接受范围
- 特殊字符和格式内容处理稳定
- 异常输入能够正确处理并返回合理错误信息

## 🔄 持续优化

根据测试结果，可以进一步优化：
1. 调整测试数据比例
2. 增加更多边界场景
3. 优化测试参数配置
4. 扩展监控维度
